package com.trs.police.service.node.spark;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.spark.impl.SparkFilterNode;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SparkFilterNode测试类 - 纯Spark实现测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class SparkFilterNodeTest {

    private SparkSession spark;
    private SparkFilterNode sparkFilterNode;
    private NodeMeta nodeMeta;

    @BeforeEach
    void setUp() {
        // 创建Spark会话（测试模式）
        spark = SparkSession.builder()
                .appName("SparkFilterNodeTest")
                .master("local[*]")
                .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
                .getOrCreate();

        // 创建节点元数据
        nodeMeta = new NodeMeta();
        nodeMeta.setUuid("test-filter-node");
        nodeMeta.setName("测试过滤节点");
    }

    @Test
    void testSimpleFilter() {
        // 准备测试数据
        NodeData inputData = createTestData();
        
        // 创建简单过滤条件：age > 25
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "{\"key\":\"age\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"25\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        
        // 创建Spark过滤节点
        sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        // 执行过滤
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkFilterNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        
        // 验证过滤结果：应该只有age > 25的记录
        for (List<FieldValue> row : result.getData()) {
            FieldValue ageField = row.stream()
                    .filter(f -> "age".equals(f.getEnName()))
                    .findFirst()
                    .orElse(null);
            assertNotNull(ageField);
            assertTrue(Integer.parseInt(ageField.getValue()) > 25);
        }
    }

    @Test
    void testComplexFilter() {
        // 准备测试数据
        NodeData inputData = createTestData();
        
        // 创建复合过滤条件：(age >= 20 AND age <= 40) AND status = 'active'
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "(",
            "{\"key\":\"age\",\"operator\":\"ge\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"20\"]}}",
            "且",
            "{\"key\":\"age\",\"operator\":\"le\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"40\"]}}",
            ")",
            "且",
            "{\"key\":\"status\",\"operator\":\"eq\",\"value\":{\"type\":\"STRING\",\"value\":[\"active\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        
        // 创建Spark过滤节点
        sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        // 执行过滤
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkFilterNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        
        // 验证过滤结果
        for (List<FieldValue> row : result.getData()) {
            FieldValue ageField = row.stream()
                    .filter(f -> "age".equals(f.getEnName()))
                    .findFirst()
                    .orElse(null);
            FieldValue statusField = row.stream()
                    .filter(f -> "status".equals(f.getEnName()))
                    .findFirst()
                    .orElse(null);
            
            assertNotNull(ageField);
            assertNotNull(statusField);
            
            int age = Integer.parseInt(ageField.getValue());
            assertTrue(age >= 20 && age <= 40);
            assertEquals("active", statusField.getValue());
        }
    }

    @Test
    void testInOperator() {
        // 准备测试数据
        NodeData inputData = createTestData();
        
        // 创建IN操作过滤条件：department IN ('IT', 'HR')
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "{\"key\":\"department\",\"operator\":\"in\",\"value\":{\"type\":\"STRING\",\"value\":[\"IT\",\"HR\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        
        // 创建Spark过滤节点
        sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        // 执行过滤
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkFilterNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        
        // 验证过滤结果
        for (List<FieldValue> row : result.getData()) {
            FieldValue deptField = row.stream()
                    .filter(f -> "department".equals(f.getEnName()))
                    .findFirst()
                    .orElse(null);
            
            assertNotNull(deptField);
            assertTrue(Arrays.asList("IT", "HR").contains(deptField.getValue()));
        }
    }

    @Test
    void testLikeOperator() {
        // 准备测试数据
        NodeData inputData = createTestData();
        
        // 创建LIKE操作过滤条件：name LIKE '张'
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "{\"key\":\"name\",\"operator\":\"like\",\"value\":{\"type\":\"STRING\",\"value\":[\"张\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        
        // 创建Spark过滤节点
        sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        // 执行过滤
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkFilterNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        
        // 验证过滤结果
        for (List<FieldValue> row : result.getData()) {
            FieldValue nameField = row.stream()
                    .filter(f -> "name".equals(f.getEnName()))
                    .findFirst()
                    .orElse(null);
            
            assertNotNull(nameField);
            assertTrue(nameField.getValue().contains("张"));
        }
    }

    @Test
    void testSparkSQLEquivalence() {
        // 准备测试数据
        NodeData inputData = createTestData();

        // 创建复杂过滤条件并验证Spark SQL转换
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "(",
            "{\"key\":\"age\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"25\"]}}",
            "且",
            "{\"key\":\"department\",\"operator\":\"in\",\"value\":{\"type\":\"STRING\",\"value\":[\"IT\",\"HR\"]}}",
            ")",
            "或",
            "{\"key\":\"name\",\"operator\":\"like\",\"value\":{\"type\":\"STRING\",\"value\":[\"张\"]}}"
        });

        String propertiesJson = JSONObject.toJSONString(properties);

        // 创建Spark过滤节点
        sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);

        // 执行过滤
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());

        NodeData result = sparkFilterNode.process(inputNodes, context);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());

        // 验证过滤逻辑：(age > 25 AND department IN ('IT', 'HR')) OR name LIKE '%张%'
        for (List<FieldValue> row : result.getData()) {
            FieldValue ageField = getFieldValue(row, "age");
            FieldValue deptField = getFieldValue(row, "department");
            FieldValue nameField = getFieldValue(row, "name");

            int age = Integer.parseInt(ageField.getValue());
            String dept = deptField.getValue();
            String name = nameField.getValue();

            // 验证逻辑：(age > 25 AND dept IN ('IT', 'HR')) OR name contains '张'
            boolean condition1 = age > 25 && (dept.equals("IT") || dept.equals("HR"));
            boolean condition2 = name.contains("张");

            assertTrue(condition1 || condition2,
                String.format("Row should match filter: age=%d, dept=%s, name=%s", age, dept, name));
        }
    }

    @Test
    void testFieldComparison() {
        // 测试字段间比较（如果支持的话）
        // 这里可以添加字段间比较的测试用例
        assertTrue(true); // 占位测试
    }

    @Test
    void testControlParameters() {
        // 测试控制参数（如果支持的话）
        // 这里可以添加控制参数的测试用例
        assertTrue(true); // 占位测试
    }

    private FieldValue getFieldValue(List<FieldValue> row, String fieldName) {
        return row.stream()
                .filter(f -> fieldName.equals(f.getEnName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建测试数据
     */
    private NodeData createTestData() {
        NodeData nodeData = new NodeData();
        
        // 创建表头
        List<FieldInfoVO> headers = Arrays.asList(
            createFieldInfo("name", "姓名", 0),
            createFieldInfo("age", "年龄", 1),
            createFieldInfo("department", "部门", 2),
            createFieldInfo("status", "状态", 3)
        );
        nodeData.setHeader(headers);
        
        // 创建数据行
        List<List<FieldValue>> data = Arrays.asList(
            createDataRow("张三", "28", "IT", "active"),
            createDataRow("李四", "32", "HR", "active"),
            createDataRow("王五", "24", "Finance", "inactive"),
            createDataRow("张六", "35", "IT", "active"),
            createDataRow("赵七", "29", "HR", "pending"),
            createDataRow("钱八", "22", "IT", "active"),
            createDataRow("孙九", "45", "Finance", "active"),
            createDataRow("周十", "31", "Marketing", "inactive")
        );
        nodeData.setData(data);
        nodeData.setTotalCount((long) data.size());
        
        return nodeData;
    }

    // 辅助方法
    private FieldInfoVO createFieldInfo(String enName, String cnName, int colIndex) {
        FieldInfoVO fieldInfo = new FieldInfoVO();
        fieldInfo.setEnName(enName);
        fieldInfo.setCnName(cnName);
        fieldInfo.setColIndex(colIndex);
        fieldInfo.setTypeCode("1"); // 默认字符串类型
        return fieldInfo;
    }

    private List<FieldValue> createDataRow(String... values) {
        List<FieldValue> row = new ArrayList<>();
        String[] fieldNames = {"name", "age", "department", "status"};
        
        for (int i = 0; i < values.length; i++) {
            FieldValue fieldValue = new FieldValue();
            fieldValue.setEnName(fieldNames[i]);
            fieldValue.setValue(values[i]);
            fieldValue.setColIndex(i);
            fieldValue.setTypeCode("1"); // 默认字符串类型
            row.add(fieldValue);
        }
        return row;
    }
}
