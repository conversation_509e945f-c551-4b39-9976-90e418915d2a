package com.trs.police.service.node.spark;

import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.dto.node.properties.StatisticNodeProperties;
import com.trs.police.dto.node.properties.bean.GroupField;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.spark.impl.SparkStatisticNode;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import org.apache.spark.sql.SparkSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SparkStatisticNode多分组字段测试
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class SparkStatisticNodeTest {

    private SparkSession spark;
    private SparkStatisticNode sparkStatisticNode;
    private NodeMeta nodeMeta;

    @BeforeEach
    void setUp() {
        // 创建Spark会话（测试模式）
        spark = SparkSession.builder()
                .appName("SparkStatisticNodeTest")
                .master("local[*]")
                .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
                .getOrCreate();

        // 创建节点元数据
        nodeMeta = new NodeMeta();
        nodeMeta.setUuid("test-statistic-node");
    }

    @Test
    void testMultipleGroupFieldsStatistic() {
        // 准备测试数据
        NodeData inputData = createTestData();
        
        // 创建统计节点配置
        StatisticNodeProperties properties = createMultiGroupProperties();
        String propertiesJson = convertToJson(properties);
        
        // 创建Spark统计节点
        sparkStatisticNode = new SparkStatisticNode(nodeMeta, propertiesJson, spark);
        
        // 执行统计
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkStatisticNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().size() > 0);
        
        // 验证表头包含所有分组字段和统计字段
        List<FieldInfoVO> headers = result.getHeader();
        assertTrue(headers.stream().anyMatch(h -> "region".equals(h.getEnName())));
        assertTrue(headers.stream().anyMatch(h -> "category".equals(h.getEnName())));
        assertTrue(headers.stream().anyMatch(h -> "total_amount".equals(h.getEnName())));
        
        // 验证数据行包含正确的字段数量
        for (List<FieldValue> row : result.getData()) {
            assertEquals(3, row.size()); // region + category + total_amount
        }
    }

    @Test
    void testTimeGroupFieldStatistic() {
        // 准备包含时间字段的测试数据
        NodeData inputData = createTimeTestData();
        
        // 创建包含时间分组的配置
        StatisticNodeProperties properties = createTimeGroupProperties();
        String propertiesJson = convertToJson(properties);
        
        // 创建Spark统计节点
        sparkStatisticNode = new SparkStatisticNode(nodeMeta, propertiesJson, spark);
        
        // 执行统计
        List<NodeData> inputNodes = Arrays.asList(inputData);
        NodeContext context = new NodeContext(new ArrayList<>());
        
        NodeData result = sparkStatisticNode.process(inputNodes, context);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getData());
        
        // 验证时间分组字段存在
        List<FieldInfoVO> headers = result.getHeader();
        assertTrue(headers.stream().anyMatch(h -> "region".equals(h.getEnName())));
        assertTrue(headers.stream().anyMatch(h -> "create_time".equals(h.getEnName())));
        assertTrue(headers.stream().anyMatch(h -> "monthly_count".equals(h.getEnName())));
    }

    /**
     * 创建测试数据
     */
    private NodeData createTestData() {
        NodeData nodeData = new NodeData();
        
        // 创建表头
        List<FieldInfoVO> headers = Arrays.asList(
            createFieldInfo("region", "地区", 0),
            createFieldInfo("category", "类别", 1),
            createFieldInfo("amount", "金额", 2)
        );
        nodeData.setHeader(headers);
        
        // 创建数据行
        List<List<FieldValue>> data = Arrays.asList(
            createDataRow("北京", "A类", "1000"),
            createDataRow("北京", "A类", "1200"),
            createDataRow("北京", "B类", "800"),
            createDataRow("上海", "A类", "1500"),
            createDataRow("上海", "B类", "900"),
            createDataRow("广州", "A类", "1100")
        );
        nodeData.setData(data);
        nodeData.setTotalCount((long) data.size());
        
        return nodeData;
    }

    /**
     * 创建包含时间字段的测试数据
     */
    private NodeData createTimeTestData() {
        NodeData nodeData = new NodeData();
        
        // 创建表头
        List<FieldInfoVO> headers = Arrays.asList(
            createFieldInfo("region", "地区", 0),
            createFieldInfo("create_time", "创建时间", 1),
            createFieldInfo("amount", "金额", 2)
        );
        nodeData.setHeader(headers);
        
        // 创建数据行
        List<List<FieldValue>> data = Arrays.asList(
            createDataRow("北京", "2023-01-15 10:30:00", "1000"),
            createDataRow("北京", "2023-01-20 14:20:00", "1200"),
            createDataRow("北京", "2023-02-10 09:15:00", "800"),
            createDataRow("上海", "2023-01-25 16:45:00", "1500"),
            createDataRow("上海", "2023-02-05 11:30:00", "900")
        );
        nodeData.setData(data);
        nodeData.setTotalCount((long) data.size());
        
        return nodeData;
    }

    /**
     * 创建多分组字段配置
     */
    private StatisticNodeProperties createMultiGroupProperties() {
        StatisticNodeProperties properties = new StatisticNodeProperties();
        
        // 设置分组字段（多个）
        List<GroupField> groupFields = Arrays.asList(
            createGroupField("region", "地区"),
            createGroupField("category", "类别")
        );
        properties.setGroupField(groupFields);
        
        // 设置统计字段
        ValueMateBase statisticValue = new ValueMateBase();
        statisticValue.setEnName("amount");
        statisticValue.setCnName("金额");
        properties.setStatisticValue(statisticValue);
        
        // 设置输出字段
        ValueMateBase outValue = new ValueMateBase();
        outValue.setEnName("total_amount");
        outValue.setCnName("总金额");
        properties.setOutValue(outValue);
        
        // 设置统计类型（SUM）
        properties.setStatisticType(2);
        
        return properties;
    }

    /**
     * 创建时间分组配置
     */
    private StatisticNodeProperties createTimeGroupProperties() {
        StatisticNodeProperties properties = new StatisticNodeProperties();
        
        // 设置分组字段（包含时间字段）
        List<GroupField> groupFields = Arrays.asList(
            createGroupField("region", "地区"),
            createTimeGroupField("create_time", "创建时间", 6) // 按月分组
        );
        properties.setGroupField(groupFields);
        
        // 设置统计字段
        ValueMateBase statisticValue = new ValueMateBase();
        statisticValue.setEnName("amount");
        statisticValue.setCnName("金额");
        properties.setStatisticValue(statisticValue);
        
        // 设置输出字段
        ValueMateBase outValue = new ValueMateBase();
        outValue.setEnName("monthly_count");
        outValue.setCnName("月度统计");
        properties.setOutValue(outValue);
        
        // 设置统计类型（COUNT）
        properties.setStatisticType(1);
        
        return properties;
    }

    // 辅助方法
    private FieldInfoVO createFieldInfo(String enName, String cnName, int colIndex) {
        FieldInfoVO fieldInfo = new FieldInfoVO();
        fieldInfo.setEnName(enName);
        fieldInfo.setCnName(cnName);
        fieldInfo.setColIndex(colIndex);
        return fieldInfo;
    }

    private List<FieldValue> createDataRow(String... values) {
        List<FieldValue> row = new ArrayList<>();
        for (int i = 0; i < values.length; i++) {
            FieldValue fieldValue = new FieldValue();
            fieldValue.setValue(values[i]);
            fieldValue.setColIndex(i);
            row.add(fieldValue);
        }
        return row;
    }

    private GroupField createGroupField(String enName, String cnName) {
        GroupField groupField = new GroupField();
        groupField.setEnName(enName);
        groupField.setCnName(cnName);
        return groupField;
    }

    private GroupField createTimeGroupField(String enName, String cnName, Integer groupType) {
        GroupField groupField = new GroupField();
        groupField.setEnName(enName);
        groupField.setCnName(cnName);
        groupField.setGroupType(groupType);
        return groupField;
    }

    private String convertToJson(StatisticNodeProperties properties) {
        // 这里应该使用实际的JSON序列化库
        // 为了测试简化，返回一个模拟的JSON字符串
        return "{}";
    }
}
