package com.trs.police.service.node.spark.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.common.utils.StringUtils;
import com.trs.common.utils.expression.EmtpyExpression;
import com.trs.common.utils.expression.Expression;
import com.trs.police.dto.node.properties.TableNodeProperties;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.DataTableOverviewDto;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.FilterNodeConditionParser;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.vo.DataTableFieldVO;
import com.trs.web.builder.base.RestfulResultsV2;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Spark表格输入节点
 *
 * <AUTHOR>
 */
public class SparkTableNode extends SparkNode {

    private DataTableService dataTableService;
    private FieldsService fieldsService;

    public SparkTableNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark, 
                         DataTableService dataTableService, FieldsService fieldsService) {
        super(nodeMeta, nodeProperties, spark);
        this.dataTableService = dataTableService;
        this.fieldsService = fieldsService;
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        TableNodeProperties property = getPropertyAs(TableNodeProperties.class);

        List<DataTableFieldVO> fieldInfo = fieldsService.getFieldInfo(property.getTableId().intValue(), 1);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<FieldInfoVO> header = fieldInfo.stream()
                .map(f -> {
                    FieldInfoVO field = new FieldInfoVO();
                    field.setCnName(f.getFieldNameCn());
                    field.setEnName(f.getFieldName());
                    field.setTypeCode(f.getFieldType());
                    field.setColIndex(atomicInteger.getAndIncrement());
                    return field;
                })
                .collect(Collectors.toList());

        NodeData nodeData = new NodeData();
        nodeData.setHeader(header);

        DataTableOverviewDto dto = new DataTableOverviewDto();
        dto.setTableId(property.getTableId());
        Integer count = BeanFactoryHolder.getEnv().getProperty("label.web.node.table.default.count", Integer.class, 2000);
        dto.setPageSize(count);

        // 构造条件
        FilterNodeConditionParser parser = new FilterNodeConditionParser(context);
        Expression expression = StringUtils.isNotEmpty(property.getTokens())
                ? parser.parseCondition(property.getTokens())
                : new EmtpyExpression();
        
        RestfulResultsV2<JSONObject> dataOverview = dataTableService.getData(dto, expression);
        List<JSONObject> datas = dataOverview.getDatas();
        
        // 使用Spark处理数据
        Dataset<Row> sparkDataset = createSparkDatasetFromJsonData(datas, header);
        
        // 可以在这里添加Spark特有的优化处理
        sparkDataset = sparkDataset.cache(); // 缓存数据集
        
        // 转换回NodeData格式
        NodeData result = datasetToNodeData(sparkDataset, nodeData);
        result.setTotalCount(dataOverview.getSummary().getTotal());
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.TABLE;
    }

    /**
     * 从JSON数据创建Spark Dataset
     *
     * @param jsonData JSON数据列表
     * @param header 表头信息
     * @return Spark Dataset
     */
    private Dataset<Row> createSparkDatasetFromJsonData(List<JSONObject> jsonData, List<FieldInfoVO> header) {
        // 将JSON数据转换为NodeData格式，然后转换为Dataset
        List<List<FieldValue>> data = jsonData.stream()
                .map(d -> mapToFieldValue(header, d))
                .collect(Collectors.toList());

        NodeData tempNodeData = new NodeData();
        tempNodeData.setHeader(header);
        tempNodeData.setData(data);
        tempNodeData.setTotalCount((long) data.size());

        return nodeDataToDataset(tempNodeData);
    }

    /**
     * 将JSON对象映射为FieldValue列表
     *
     * @param header 表头信息
     * @param jsonObject JSON对象
     * @return FieldValue列表
     */
    private List<FieldValue> mapToFieldValue(List<FieldInfoVO> header, JSONObject jsonObject) {
        return header.stream()
                .map(h -> {
                    FieldValue fieldValue = new FieldValue();
                    fieldValue.setEnName(h.getEnName());
                    fieldValue.setColIndex(h.getColIndex());
                    Object value = jsonObject.get(h.getEnName());
                    fieldValue.setValue(value != null ? value.toString() : null);
                    return fieldValue;
                })
                .collect(Collectors.toList());
    }
}
