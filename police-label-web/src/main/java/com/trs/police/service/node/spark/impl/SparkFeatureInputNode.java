package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.FeatureInputProperties;
import com.trs.police.dto.node.properties.bean.ControlValue;
import com.trs.police.dto.node.ControlDTO;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.node.spark.SparkNodeService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.spark.sql.SparkSession;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Spark特征输入节点
 *
 * <AUTHOR>
 */
public class SparkFeatureInputNode extends SparkNode {

    private FeatureFacade featureFacade;

    public SparkFeatureInputNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark, FeatureFacade featureFacade) {
        super(nodeMeta, nodeProperties, spark);
        this.featureFacade = featureFacade;
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FeatureInputProperties property = getPropertyAs(FeatureInputProperties.class);
        ProcessDTO process = featureFacade.getFeatureProcess(property.getFeatureId());
        
        // 上下文添加控制参数的值
        for (ControlValue controlValue : Optional.ofNullable(property.getControlValue()).orElse(new ArrayList<>())) {
            Optional<ControlDTO> first = process.getControl().stream()
                    .filter(c -> c.getName().equals(controlValue.getControlName()))
                    .findAny();
            if (first.isEmpty()) {
                throw new RuntimeException("特征输入节点配置错误，没有找到控制参数：" + controlValue.getControlName());
            }
            context.getControlValueMap().put(controlValue.getControlName(), controlValue(first.get().getTypeCode(), controlValue));
        }
        
        // 使用Spark节点服务处理特征流程
        SparkNodeService sparkNodeService = BeanFactoryHolder.getBean(SparkNodeService.class).get();
        NodeData nodeData = sparkNodeService.previewNode(process);
        
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FEATURE_IN;
    }

    /**
     * 控制参数值转换
     *
     * @param typeCode 类型代码
     * @param controlValue 控制值
     * @return 转换后的值
     */
    private Object controlValue(Integer typeCode, ControlValue controlValue) {
        // 这里需要根据类型代码进行值转换
        // 简化实现，实际需要根据具体的类型代码进行转换
        String value = controlValue.getValue();
        if (value == null) {
            return null;
        }
        
        switch (typeCode) {
            case 1: // 字符串类型
                return value;
            case 2: // 整数类型
                try {
                    return Integer.parseInt(value);
                } catch (NumberFormatException e) {
                    return value;
                }
            case 3: // 小数类型
                try {
                    return Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    return value;
                }
            case 4: // 布尔类型
                return Boolean.parseBoolean(value);
            default:
                return value;
        }
    }
}
