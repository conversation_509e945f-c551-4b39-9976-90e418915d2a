package com.trs.police.service.node;

import com.trs.police.common.core.excpetion.TRSException;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.dto.node.NodeDTO;
import com.trs.police.dto.node.OrderDTO;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.spark.SparkNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 节点服务
 *
 * <AUTHOR>
 */
@Service
public class NodeService {

    @Autowired
    private NodeFactory nodeFactory;

    @Autowired(required = false)
    private SparkNodeService sparkNodeService;

    @Value("${spark.enabled:false}")
    private boolean sparkEnabled;

    /**
     * 预览节点
     *
     * @param dto dto
     * @return 节点数据
     */
    public NodeData previewNode(ProcessDTO dto) {
        // 如果启用了Spark且Spark服务可用，则使用Spark实现
        if (sparkEnabled && sparkNodeService != null) {
            try {
                return sparkNodeService.previewNode(dto);
            } catch (Exception e) {
                // 如果Spark处理失败，回退到原始实现
                // 可以根据需要记录日志
                System.err.println("Spark处理失败，回退到原始实现: " + e.getMessage());
            }
        }

        // 使用原始实现
        String currentNode = dto.getCurrentNode();
        // 匹配到对应的节点
        Optional<NodeDTO> node = dto.getNodes().stream()
                .filter(nd -> currentNode.equals(nd.getNodeMeta().getUuid()))
                .findAny();
        if (node.isEmpty()) {
            throw new TRSException(String.format("%s节点不存在", currentNode));
        }
        // 判断节点类型，如果是输入节点，直接返回数据
        NodeDTO nodeDTO = node.get();
        Node n = nodeFactory.nodeOf(nodeDTO.getNodeMeta().getNodeTypeCode(), nodeDTO.getNodeMeta(), nodeDTO.getNodeProperties()).get();
        NodeContext context = new NodeContext(dto.getControl());
        if (n.isInputNode()) {
            return n.output(new ArrayList<>(), context);
        }
        // 非输入节点，需要根据当前节点的输入节点，获取输入节点的数据，然后进行节点处理
        List<NodeData> source = dto.getNodeOrders()
                .stream()
                .filter(od -> currentNode.equals(od.getTo()))
                .map(OrderDTO::getFrom)
                .map(from -> {
                    ProcessDTO clone = dto.copy();
                    clone.setCurrentNode(from);
                    return previewNode(clone);
                })
                .collect(Collectors.toList());
        return n.output(source, context);
    }
}
