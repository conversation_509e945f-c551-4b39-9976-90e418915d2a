package com.trs.police.service.node.spark;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.ValueMateBase;
import com.trs.police.dto.node.properties.NodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.constant.NodeType;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Spark节点基类
 * 
 * <AUTHOR>
 */
public abstract class SparkNode {

    /**
     * 节点信息
     */
    protected NodeMeta nodeMeta;

    /**
     * 节点属性 json字符串
     */
    protected String nodeProperties;

    /**
     * Spark会话
     */
    protected SparkSession spark;

    public SparkNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        this.nodeMeta = nodeMeta;
        this.nodeProperties = nodeProperties;
        this.spark = spark;
    }

    /**
     * 根据输入数据获取到输出数据
     *
     * @param inputNode 输入节点
     * @param context 上下文信息
     * @return 数据
     */
    public NodeData output(List<NodeData> inputNode, NodeContext context) {
        NodeData process = process(inputNode, context);
        // 根据过滤参数过滤出字段
        Set<String> set = nodeMeta.getOutputRowMeta().getValueMetaList()
                .stream()
                .map(ValueMateBase::getEnName)
                .collect(Collectors.toSet());

        // 表头
        List<FieldInfoVO> header = process.getHeader().stream()
                .filter(h -> set.contains(h.getEnName()))
                .collect(Collectors.toList());
        process.setHeader(header);

        // 数据
        List<List<FieldValue>> data = process.getData()
                .stream()
                .map(row -> {
                    return row.stream()
                            .filter(c -> set.contains(c.getEnName()))
                            .collect(Collectors.toList());
                })
                .collect(Collectors.toList());
        process.setData(data);
        return process;
    }

    /**
     * 根据输入数据获取到输出数据（Spark实现）
     *
     * @param inputNode 输入节点
     * @param context 上下文
     * @return 数据
     */
    protected abstract NodeData process(List<NodeData> inputNode, NodeContext context);

    /**
     * 节点类型 {@link NodeType}
     *
     * @return 节点类型
     */
    public abstract Integer nodeType();

    /**
     * 获取属性
     *
     * @param clazz 类型
     * @param <T> 属性
     * @return 属性
     */
    public <T extends NodeProperties> T getPropertyAs(Class<T> clazz) {
        return JSONObject.parseObject(nodeProperties, clazz);
    }

    /**
     * 是否是输入节点
     *
     * @return 布尔
     */
    public Boolean isInputNode() {
        return NodeType.TABLE.equals(nodeMeta.getNodeTypeCode());
    }

    /**
     * 将NodeData转换为Spark Dataset
     *
     * @param nodeData 节点数据
     * @return Spark Dataset
     */
    protected Dataset<Row> nodeDataToDataset(NodeData nodeData) {
        // 这里需要实现NodeData到Dataset的转换逻辑
        // 由于NodeData包含FieldValue列表，需要转换为Spark Row格式
        return SparkDataConverter.convertNodeDataToDataset(spark, nodeData);
    }

    /**
     * 将Spark Dataset转换为NodeData
     *
     * @param dataset Spark Dataset
     * @param originalNodeData 原始节点数据（用于获取元数据）
     * @return 节点数据
     */
    protected NodeData datasetToNodeData(Dataset<Row> dataset, NodeData originalNodeData) {
        // 这里需要实现Dataset到NodeData的转换逻辑
        return SparkDataConverter.convertDatasetToNodeData(dataset, originalNodeData, nodeMeta);
    }
}
