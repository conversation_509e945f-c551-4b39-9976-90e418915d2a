package com.trs.police.service.label;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.enums.LabelCalculationStatus;
import com.trs.police.mapper.LabelCalculationTaskMapper;
import com.trs.police.service.label.impl.LabelCalculationManager;
import com.trs.police.vo.label.SparkJobState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签调度服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LabelScheduleService {

    @Autowired
    private LabelCalculationService calculationService;

    @Autowired
    private LabelCalculationTaskMapper taskMapper;

    @Autowired
    private LabelCalculationManager labelCalculationManager;

    /**
     * 定时检查并执行标签计算
     * 每分钟执行一次
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void scheduleCalculation() {
        log.debug("开始执行标签定时计算检查");

        try {
            // 获取所有需要定时计算的标签
            List<Long> scheduledLabels = calculationService.getScheduledLabels();
            
            if (scheduledLabels.isEmpty()) {
                log.debug("没有需要定时计算的标签");
                return;
            }

            log.info("检查到 {} 个定时标签", scheduledLabels.size());

            int triggeredCount = 0;
            for (Long labelId : scheduledLabels) {
                try {
                    // 检查是否需要执行计算
                    if (calculationService.shouldCalculate(labelId)) {
                        String taskId = calculationService.triggerScheduledCalculation(labelId);
                        if (taskId != null) {
                            triggeredCount++;
                            log.info("触发标签定时计算: labelId={}, taskId={}", labelId, taskId);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理标签定时计算失败: labelId={}", labelId, e);
                }
            }

            if (triggeredCount > 0) {
                log.info("本次定时检查触发了 {} 个标签计算任务", triggeredCount);
            }

        } catch (Exception e) {
            log.error("标签定时计算检查失败", e);
        }
    }

    /**
     * 定时清理超时任务
     * 每10分钟执行一次
     */
//    @Scheduled(cron = "0 */10 * * * ?")
    public void cleanupTimeoutTasks() {
        log.debug("开始清理超时任务");

        try {
            // 查找超时的任务（运行超过30分钟）
            List<com.trs.police.entity.label.LabelCalculationTaskDO> timeoutTasks = 
                taskMapper.findTimeoutTasks(30);

            if (timeoutTasks.isEmpty()) {
                log.debug("没有发现超时任务");
                return;
            }

            log.warn("发现 {} 个超时任务", timeoutTasks.size());

            for (com.trs.police.entity.label.LabelCalculationTaskDO task : timeoutTasks) {
                try {
                    // 更新任务状态为超时
                    taskMapper.updateTaskStatus(task.getTaskId(), 
                                              LabelCalculationStatus.TIMEOUT.getCode(),
                                              LocalDateTime.now(),
                                              "任务执行超时，自动标记为超时状态");
                    
                    log.warn("任务已标记为超时: taskId={}, labelId={}, startTime={}", 
                            task.getTaskId(), task.getLabelId(), task.getStartTime());

                } catch (Exception e) {
                    log.error("处理超时任务失败: taskId={}", task.getTaskId(), e);
                }
            }

        } catch (Exception e) {
            log.error("清理超时任务失败", e);
        }
    }

    /**
     * 定时更新任务执行状态
     * 每10分钟执行一次
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void updateTaskStatus() {
        log.debug("开始更新任务执行状态");

        try {
            // 查找超时的任务（运行超过30分钟）
            List<com.trs.police.entity.label.LabelCalculationTaskDO> runningTasks =
                    taskMapper.findRunningTasks();

            if (runningTasks.isEmpty()) {
                log.debug("没有发现运行中任务");
                return;
            }

            log.warn("发现 {} 个运行中任务", runningTasks.size());

            for (com.trs.police.entity.label.LabelCalculationTaskDO task : runningTasks) {
                try {
                    // 更新任务状态为超时
                    SparkJobState jobState = labelCalculationManager.getJobState(task.getJobId());
                    if(!LabelCalculationStatus.RUNNING.equals(jobState.getJobState())){
                        taskMapper.updateTaskStatus(task.getTaskId(),
                                jobState.getJobState().getCode(),
                                LocalDateTime.now(), jobState.getErrorMessage());
                    }
                } catch (Exception e) {
                    log.error("更新任务执行状态失败: taskId={}", task.getTaskId(), e);
                }
            }

        } catch (Exception e) {
            log.error("更新任务执行状态失败", e);
        }
    }

    /**
     * 定时统计任务执行情况
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void statisticsReport() {
        log.debug("开始生成任务执行统计报告");

        try {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(1);

            // 统计各状态的任务数量
            long pendingCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.PENDING.getCode());
            long runningCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.RUNNING.getCode());
            long successCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.SUCCESS.getCode());
            long failedCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.FAILED.getCode());
            long cancelledCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.CANCELLED.getCode());
            long timeoutCount = countTasksByStatus(startTime, endTime, LabelCalculationStatus.TIMEOUT.getCode());

            long totalCount = pendingCount + runningCount + successCount + failedCount + cancelledCount + timeoutCount;

            if (totalCount > 0) {
                log.info("过去1小时标签计算任务统计: 总计={}, 等待={}, 运行中={}, 成功={}, 失败={}, 取消={}, 超时={}",
                        totalCount, pendingCount, runningCount, successCount, failedCount, cancelledCount, timeoutCount);

                // 计算成功率
                if (successCount + failedCount + timeoutCount > 0) {
                    double successRate = (double) successCount / (successCount + failedCount + timeoutCount) * 100;
                    log.info("过去1小时标签计算成功率: {:.2f}%", successRate);
                }
            }

        } catch (Exception e) {
            log.error("生成任务执行统计报告失败", e);
        }
    }

    /**
     * 统计指定时间范围和状态的任务数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 状态
     * @return 任务数量
     */
    private long countTasksByStatus(LocalDateTime startTime, LocalDateTime endTime, String status) {
        try {
            List<com.trs.police.entity.label.LabelCalculationTaskDO> tasks = 
                taskMapper.findByTimeRange(startTime, endTime, status);
            return tasks.size();
        } catch (Exception e) {
            log.error("统计任务数量失败: status={}", status, e);
            return 0;
        }
    }

    /**
     * 手动触发所有需要计算的标签
     * 仅用于测试或紧急情况
     */
    public void triggerAllScheduledCalculations() {
        log.info("手动触发所有需要计算的标签");

        try {
            List<Long> scheduledLabels = calculationService.getScheduledLabels();
            
            if (scheduledLabels.isEmpty()) {
                log.info("没有需要计算的标签");
                return;
            }

            int triggeredCount = 0;
            for (Long labelId : scheduledLabels) {
                try {
                    if (calculationService.shouldCalculate(labelId)) {
                        String taskId = calculationService.triggerScheduledCalculation(labelId);
                        if (taskId != null) {
                            triggeredCount++;
                            log.info("手动触发标签计算: labelId={}, taskId={}", labelId, taskId);
                        }
                    }
                } catch (Exception e) {
                    log.error("手动触发标签计算失败: labelId={}", labelId, e);
                }
            }

            log.info("手动触发完成，共触发 {} 个标签计算任务", triggeredCount);

        } catch (Exception e) {
            log.error("手动触发所有标签计算失败", e);
        }
    }

    /**
     * 获取当前运行中的任务数量
     *
     * @return 运行中的任务数量
     */
    public long getRunningTaskCount() {
        try {
            return taskMapper.findRunningTasks().size();
        } catch (Exception e) {
            log.error("获取运行中任务数量失败", e);
            return 0;
        }
    }
}
