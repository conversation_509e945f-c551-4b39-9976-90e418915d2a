package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.ConvertNodeProperties;
import com.trs.police.dto.node.properties.bean.ConvertItem;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * Spark转换节点
 *
 * <AUTHOR>
 */
public class SparkConvertNode extends SparkNode {

    public SparkConvertNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        ConvertNodeProperties property = getPropertyAs(ConvertNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用转换操作
        Dataset<Row> convertedDataset = applySparkConvert(dataset, property);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(convertedDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.TRANSFORM;
    }

    /**
     * 应用Spark转换操作
     *
     * @param dataset 数据集
     * @param property 转换属性
     * @return 转换后的数据集
     */
    private Dataset<Row> applySparkConvert(Dataset<Row> dataset, ConvertNodeProperties property) {
        if (property.getConvertField() == null || property.getConvertField().isEmpty()) {
            return dataset;
        }

        Dataset<Row> result = dataset;

        // 应用每个转换规则
        for (ConvertItem convertItem : property.getConvertField()) {
            result = applyConvertItem(result, convertItem);
        }

        return result;
    }

    /**
     * 应用单个转换项
     *
     * @param dataset 数据集
     * @param convertItem 转换项
     * @return 转换后的数据集
     */
    private Dataset<Row> applyConvertItem(Dataset<Row> dataset, ConvertItem convertItem) {
        String fromField = convertItem.getFrom().getEnName();
        String toField = convertItem.getTo().getEnName();
        String convertType = convertItem.getConvertType();

        // 根据转换类型应用不同的转换逻辑
        switch (convertType) {
            case "1": // 字符串转换
                return dataset.withColumn(toField, functions.col(fromField).cast("string"));
            case "2": // 数字转换
                return dataset.withColumn(toField, functions.col(fromField).cast("double"));
            case "3": // 日期转换
                return dataset.withColumn(toField, functions.to_date(functions.col(fromField)));
            case "4": // 时间戳转换
                return dataset.withColumn(toField, functions.to_timestamp(functions.col(fromField)));
            case "5": // 大写转换
                return dataset.withColumn(toField, functions.upper(functions.col(fromField)));
            case "6": // 小写转换
                return dataset.withColumn(toField, functions.lower(functions.col(fromField)));
            case "7": // 去空格
                return dataset.withColumn(toField, functions.trim(functions.col(fromField)));
            case "8": // 替换操作
                return applyReplaceConvert(dataset, convertItem, fromField, toField);
            case "9": // 截取操作
                return applySubstringConvert(dataset, convertItem, fromField, toField);
            case "10": // 格式化操作
                return applyFormatConvert(dataset, convertItem, fromField, toField);
            default:
                // 默认直接复制字段
                return dataset.withColumn(toField, functions.col(fromField));
        }
    }

    /**
     * 应用替换转换
     *
     * @param dataset 数据集
     * @param convertItem 转换项
     * @param fromField 源字段
     * @param toField 目标字段
     * @return 转换后的数据集
     */
    private Dataset<Row> applyReplaceConvert(Dataset<Row> dataset, ConvertItem convertItem, 
                                           String fromField, String toField) {
        // 这里需要从convertItem中获取替换的源字符串和目标字符串
        // 假设在convertItem的某个属性中存储了这些信息
        String oldValue = convertItem.getOldValue(); // 需要在ConvertItem中添加这个属性
        String newValue = convertItem.getNewValue(); // 需要在ConvertItem中添加这个属性
        
        if (oldValue != null && newValue != null) {
            return dataset.withColumn(toField, 
                    functions.regexp_replace(functions.col(fromField), oldValue, newValue));
        } else {
            return dataset.withColumn(toField, functions.col(fromField));
        }
    }

    /**
     * 应用截取转换
     *
     * @param dataset 数据集
     * @param convertItem 转换项
     * @param fromField 源字段
     * @param toField 目标字段
     * @return 转换后的数据集
     */
    private Dataset<Row> applySubstringConvert(Dataset<Row> dataset, ConvertItem convertItem, 
                                             String fromField, String toField) {
        // 这里需要从convertItem中获取截取的起始位置和长度
        Integer startPos = convertItem.getStartPos(); // 需要在ConvertItem中添加这个属性
        Integer length = convertItem.getLength(); // 需要在ConvertItem中添加这个属性
        
        if (startPos != null && length != null) {
            return dataset.withColumn(toField, 
                    functions.substring(functions.col(fromField), startPos, length));
        } else if (startPos != null) {
            return dataset.withColumn(toField, 
                    functions.substring(functions.col(fromField), startPos, Integer.MAX_VALUE));
        } else {
            return dataset.withColumn(toField, functions.col(fromField));
        }
    }

    /**
     * 应用格式化转换
     *
     * @param dataset 数据集
     * @param convertItem 转换项
     * @param fromField 源字段
     * @param toField 目标字段
     * @return 转换后的数据集
     */
    private Dataset<Row> applyFormatConvert(Dataset<Row> dataset, ConvertItem convertItem, 
                                          String fromField, String toField) {
        // 这里需要从convertItem中获取格式化模板
        String formatPattern = convertItem.getFormatPattern(); // 需要在ConvertItem中添加这个属性
        
        if (formatPattern != null) {
            // 根据格式化模板应用不同的格式化逻辑
            if (formatPattern.contains("yyyy") || formatPattern.contains("MM") || formatPattern.contains("dd")) {
                // 日期格式化
                return dataset.withColumn(toField, 
                        functions.date_format(functions.col(fromField), formatPattern));
            } else {
                // 其他格式化，使用format_string
                return dataset.withColumn(toField, 
                        functions.format_string(formatPattern, functions.col(fromField)));
            }
        } else {
            return dataset.withColumn(toField, functions.col(fromField));
        }
    }
}
