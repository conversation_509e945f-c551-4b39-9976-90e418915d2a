package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * Spark过滤节点
 *
 * <AUTHOR>
 */
public class SparkFilterNode extends SparkNode {

    public SparkFilterNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FilterNodeProperties property = getPropertyAs(FilterNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用过滤条件
        Dataset<Row> filteredDataset = applySparkFilter(dataset, property, context);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(filteredDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FILTER;
    }

    /**
     * 应用Spark过滤条件
     *
     * @param dataset 数据集
     * @param property 过滤属性
     * @param context 上下文
     * @return 过滤后的数据集
     */
    private Dataset<Row> applySparkFilter(Dataset<Row> dataset, FilterNodeProperties property, NodeContext context) {
        if (property.getTokens() == null || property.getTokens().length == 0) {
            return dataset;
        }

        // 这里需要将原有的过滤逻辑转换为Spark SQL表达式
        // 由于原有逻辑比较复杂，这里提供一个简化的实现示例
        String filterCondition = buildSparkFilterCondition(property, context);
        
        if (filterCondition != null && !filterCondition.isEmpty()) {
            return dataset.filter(filterCondition);
        }
        
        return dataset;
    }

    /**
     * 构建Spark过滤条件
     *
     * @param property 过滤属性
     * @param context 上下文
     * @return Spark SQL过滤条件
     */
    private String buildSparkFilterCondition(FilterNodeProperties property, NodeContext context) {
        // 这里需要将原有的token解析逻辑转换为Spark SQL语法
        // 这是一个简化的实现，实际需要根据具体的token格式进行解析
        
        StringBuilder condition = new StringBuilder();
        String[] tokens = property.getTokens();
        
        for (int i = 0; i < tokens.length; i++) {
            String token = tokens[i];
            
            // 简化的token解析逻辑
            if (token.startsWith("field:")) {
                // 字段名
                String fieldName = token.substring(6);
                condition.append("`").append(fieldName).append("`");
            } else if (token.equals("=")) {
                condition.append(" = ");
            } else if (token.equals(">")) {
                condition.append(" > ");
            } else if (token.equals("<")) {
                condition.append(" < ");
            } else if (token.equals(">=")) {
                condition.append(" >= ");
            } else if (token.equals("<=")) {
                condition.append(" <= ");
            } else if (token.equals("!=")) {
                condition.append(" != ");
            } else if (token.equals("AND")) {
                condition.append(" AND ");
            } else if (token.equals("OR")) {
                condition.append(" OR ");
            } else if (token.equals("(")) {
                condition.append("(");
            } else if (token.equals(")")) {
                condition.append(")");
            } else if (token.startsWith("value:")) {
                // 值
                String value = token.substring(6);
                if (isNumeric(value)) {
                    condition.append(value);
                } else {
                    condition.append("'").append(value.replace("'", "''")).append("'");
                }
            } else if (token.startsWith("control:")) {
                // 控制参数
                String controlName = token.substring(8);
                // 从上下文获取控制参数值
                Object controlValue = context.getControlValueMap().get(controlName);
                if (controlValue != null) {
                    if (isNumeric(controlValue.toString())) {
                        condition.append(controlValue.toString());
                    } else {
                        condition.append("'").append(controlValue.toString().replace("'", "''")).append("'");
                    }
                } else {
                    // 如果控制参数不存在，使用默认值或跳过条件
                    condition.append("NULL");
                }
            }
        }
        
        return condition.toString();
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str 字符串
     * @return 是否为数字
     */
    private boolean isNumeric(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
