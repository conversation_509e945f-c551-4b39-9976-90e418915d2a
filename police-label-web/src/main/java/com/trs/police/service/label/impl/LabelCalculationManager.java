package com.trs.police.service.label.impl;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.common.core.utils.OkHttpUtil;
import com.trs.police.dto.label.JobInput;
import com.trs.police.vo.label.SparkJobState;
import com.trs.police.vo.label.SubmitInfoVo;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */
@Component
public class LabelCalculationManager {

    OkHttpUtil okHttpUtil = OkHttpUtil.getInstance();

    /**
     * 提交标签计算任务
     *
     * @param labelId 标签ID
     * @param taskId 任务ID
     * @return 提交信息
     */
    public SubmitInfoVo submitLabelCalculation(Long labelId, String taskId) {
        JobInput jobInput = new JobInput();
        jobInput.setJobName("标签计算"+taskId);
        jobInput.setMainClass("com.trs.police.engine.controller.LabelEngineMain");
        jobInput.setInputParams("{\"labelId\":" + labelId + "}");
        jobInput.setJarFileName("police-label-engine_3.2.4_3.2.2-shaded-0.0.1-SNAPSHOT.jar");
        String url = BeanFactoryHolder.getEnv().getProperty("spark.url.job", "http://10.18.20.131:16082/spark/job");
        String res = okHttpUtil.postData(url, JSONObject.toJSONString(jobInput));
        JSONObject jsonObject = JSONObject.parseObject(res);
        String data = jsonObject.getJSONArray("data").getString(0);
        return JSONObject.parseObject(data, SubmitInfoVo.class);
    }

    /**
     * 获取任务状态
     *
     * @param jobId 任务ID
     * @return 任务状态
     */
    public SparkJobState getJobState(String jobId) {
        String url = BeanFactoryHolder.getEnv().getProperty("spark.url.jobInfo", "http://10.18.20.131:16082/spark/findJobInfo");
        String res = okHttpUtil.getData(url+"?jobId="+jobId);
        String data = JSONObject.parseObject(res).getJSONArray("data").getString(0);
        JSONObject jsonObject = JSONObject.parseObject(data);
        SparkJobState sparkJobState = new SparkJobState();
        sparkJobState.setJobState(jsonObject.getString("jobState"));
        sparkJobState.setErrorMessage(jsonObject.getString("errorMessage"));

        return sparkJobState;
    }
}
