package com.trs.police.service.node.spark;

import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.RowFactory;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.types.DataType;
import org.apache.spark.sql.types.DataTypes;
import org.apache.spark.sql.types.StructField;
import org.apache.spark.sql.types.StructType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Spark数据转换工具类
 * 
 * <AUTHOR>
 */
public class SparkDataConverter {

    /**
     * 将NodeData转换为Spark Dataset
     *
     * @param spark Spark会话
     * @param nodeData 节点数据
     * @return Spark Dataset
     */
    public static Dataset<Row> convertNodeDataToDataset(SparkSession spark, NodeData nodeData) {
        if (nodeData == null || nodeData.getData() == null || nodeData.getData().isEmpty()) {
            // 返回空的Dataset
            StructType schema = createSchemaFromHeader(nodeData.getHeader());
            return spark.createDataFrame(new ArrayList<>(), schema);
        }

        // 创建Schema
        StructType schema = createSchemaFromHeader(nodeData.getHeader());

        // 转换数据
        List<Row> rows = nodeData.getData().stream()
                .map(SparkDataConverter::convertFieldValueListToRow)
                .collect(Collectors.toList());

        return spark.createDataFrame(rows, schema);
    }

    /**
     * 将Spark Dataset转换为NodeData
     *
     * @param dataset Spark Dataset
     * @param originalNodeData 原始节点数据（用于获取元数据）
     * @param nodeMeta 节点元数据
     * @return 节点数据
     */
    public static NodeData convertDatasetToNodeData(Dataset<Row> dataset, NodeData originalNodeData, NodeMeta nodeMeta) {
        NodeData result = new NodeData();
        result.setNodeMeta(nodeMeta);

        // 设置表头
        List<FieldInfoVO> header = createHeaderFromSchema(dataset.schema());
        result.setHeader(header);

        // 转换数据
        List<Row> rows = dataset.collectAsList();
        List<List<FieldValue>> data = rows.stream()
                .map(row -> convertRowToFieldValueList(row, header))
                .collect(Collectors.toList());

        result.setData(data);
        result.setTotalCount((long) data.size());

        return result;
    }

    /**
     * 从表头创建Spark Schema
     *
     * @param header 表头信息
     * @return Spark StructType
     */
    private static StructType createSchemaFromHeader(List<FieldInfoVO> header) {
        if (header == null || header.isEmpty()) {
            return new StructType();
        }

        List<StructField> fields = header.stream()
                .map(fieldInfo -> {
                    DataType dataType = mapFieldTypeToSparkType(fieldInfo.getTypeCode());
                    return DataTypes.createStructField(fieldInfo.getEnName(), dataType, true);
                })
                .collect(Collectors.toList());

        return new StructType(fields.toArray(new StructField[0]));
    }

    /**
     * 从Spark Schema创建表头
     *
     * @param schema Spark StructType
     * @return 表头信息
     */
    private static List<FieldInfoVO> createHeaderFromSchema(StructType schema) {
        AtomicInteger colIndex = new AtomicInteger(0);
        return java.util.Arrays.stream(schema.fields())
                .map(field -> {
                    FieldInfoVO fieldInfo = new FieldInfoVO();
                    fieldInfo.setEnName(field.name());
                    fieldInfo.setCnName(field.name()); // 默认使用英文名
                    fieldInfo.setTypeCode(mapSparkTypeToFieldType(field.dataType()));
                    fieldInfo.setColIndex(colIndex.getAndIncrement());
                    return fieldInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将FieldValue列表转换为Spark Row
     *
     * @param fieldValues 字段值列表
     * @return Spark Row
     */
    private static Row convertFieldValueListToRow(List<FieldValue> fieldValues) {
        Object[] values = fieldValues.stream()
                .map(SparkDataConverter::convertFieldValueToObject)
                .toArray();
        return RowFactory.create(values);
    }

    /**
     * 将Spark Row转换为FieldValue列表
     *
     * @param row Spark Row
     * @param header 表头信息
     * @return FieldValue列表
     */
    private static List<FieldValue> convertRowToFieldValueList(Row row, List<FieldInfoVO> header) {
        List<FieldValue> fieldValues = new ArrayList<>();
        for (int i = 0; i < header.size(); i++) {
            FieldInfoVO fieldInfo = header.get(i);
            Object value = row.get(i);
            
            FieldValue fieldValue = new FieldValue();
            fieldValue.setEnName(fieldInfo.getEnName());
            fieldValue.setColIndex(fieldInfo.getColIndex());
            fieldValue.setValue(value != null ? value.toString() : null);
            
            fieldValues.add(fieldValue);
        }
        return fieldValues;
    }

    /**
     * 将FieldValue转换为Object
     *
     * @param fieldValue 字段值
     * @return Object
     */
    private static Object convertFieldValueToObject(FieldValue fieldValue) {
        if (fieldValue == null || fieldValue.getValue() == null) {
            return null;
        }
        
        String value = fieldValue.getValue();
        if (value.isEmpty()) {
            return null;
        }
        
        // 根据需要进行类型转换
        try {
            // 尝试转换为数字
            if (value.contains(".")) {
                return new BigDecimal(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，返回字符串
            return value;
        }
    }

    /**
     * 将字段类型映射到Spark数据类型
     *
     * @param fieldTypeCode 字段类型代码
     * @return Spark DataType
     */
    private static DataType mapFieldTypeToSparkType(Integer fieldTypeCode) {
        if (fieldTypeCode == null) {
            return DataTypes.StringType;
        }
        
        // 根据实际的字段类型代码进行映射
        switch (fieldTypeCode) {
            case 1: // 假设1是字符串类型
                return DataTypes.StringType;
            case 2: // 假设2是整数类型
                return DataTypes.LongType;
            case 3: // 假设3是小数类型
                return DataTypes.createDecimalType();
            case 4: // 假设4是日期类型
                return DataTypes.DateType;
            case 5: // 假设5是时间戳类型
                return DataTypes.TimestampType;
            default:
                return DataTypes.StringType;
        }
    }

    /**
     * 将Spark数据类型映射到字段类型
     *
     * @param dataType Spark DataType
     * @return 字段类型代码
     */
    private static Integer mapSparkTypeToFieldType(DataType dataType) {
        if (dataType.equals(DataTypes.StringType)) {
            return 1;
        } else if (dataType.equals(DataTypes.LongType) || dataType.equals(DataTypes.IntegerType)) {
            return 2;
        } else if (dataType.typeName().contains("decimal") || dataType.equals(DataTypes.DoubleType)) {
            return 3;
        } else if (dataType.equals(DataTypes.DateType)) {
            return 4;
        } else if (dataType.equals(DataTypes.TimestampType)) {
            return 5;
        } else {
            return 1; // 默认为字符串类型
        }
    }
}
