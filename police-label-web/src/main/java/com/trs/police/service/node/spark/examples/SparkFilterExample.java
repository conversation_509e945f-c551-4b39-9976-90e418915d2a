package com.trs.police.service.node.spark.examples;

/**
 * Spark过滤节点使用示例
 * 
 * <AUTHOR>
 */
public class SparkFilterExample {

    /**
     * SparkFilterNode过滤逻辑示例
     * 
     * 原始FilterNode支持复杂的过滤表达式，包括：
     * 1. 基本比较操作：eq, ne, gt, lt, ge, le
     * 2. 集合操作：in, notIn
     * 3. 字符串操作：like, notLike
     * 4. 空值检查：isNull, notNull, empty, notEmpty
     * 5. 正则表达式：regularMatch, regularNotMatch
     * 6. 逻辑操作：且(AND), 或(OR), 非(NOT)
     * 7. 括号分组：支持复杂的逻辑组合
     * 8. 控制参数：支持动态参数值
     * 9. 字段引用：支持字段间比较
     * 
     * tokens数组格式示例：
     * 
     * 1. 简单条件：
     * ["{"key":"age","operator":"gt","value":{"type":"NUMBER","value":["18"]}}"]
     * 含义：age > 18
     * 
     * 2. 复合条件：
     * [
     *   "(",
     *   "{"key":"age","operator":"ge","value":{"type":"NUMBER","value":["18"]}}",
     *   "且",
     *   "{"key":"age","operator":"le","value":{"type":"NUMBER","value":["65"]}}",
     *   ")",
     *   "且",
     *   "{"key":"status","operator":"in","value":{"type":"STRING","value":["active","pending"]}}"
     * ]
     * 含义：(age >= 18 AND age <= 65) AND status IN ('active', 'pending')
     * 
     * 3. 包含OR逻辑：
     * [
     *   "{"key":"department","operator":"eq","value":{"type":"STRING","value":["IT"]}}",
     *   "或",
     *   "(",
     *   "{"key":"salary","operator":"gt","value":{"type":"NUMBER","value":["50000"]}}",
     *   "且",
     *   "{"key":"experience","operator":"ge","value":{"type":"NUMBER","value":["5"]}}",
     *   ")"
     * ]
     * 含义：department = 'IT' OR (salary > 50000 AND experience >= 5)
     * 
     * 4. 字符串模糊匹配：
     * ["{"key":"name","operator":"like","value":{"type":"STRING","value":["张"]}}"]
     * 含义：name LIKE '%张%'
     * 
     * 5. 空值检查：
     * [
     *   "{"key":"email","operator":"notNull","value":{"type":"NULL","value":[]}}",
     *   "且",
     *   "{"key":"phone","operator":"notEmpty","value":{"type":"EMPTY","value":[]}}"
     * ]
     * 含义：email IS NOT NULL AND phone IS NOT EMPTY
     * 
     * 6. 正则表达式：
     * ["{"key":"phone","operator":"regularMatch","value":{"type":"STRING","value":["^1[3-9]\\d{9}$"]}}"]
     * 含义：phone 匹配手机号格式
     * 
     * 7. 控制参数：
     * ["{"key":"create_time","operator":"ge","value":{"type":"CONTROL_PARAM","value":["start_date"]}}"]
     * 含义：create_time >= ${start_date} (start_date是控制参数)
     * 
     * 8. 字段间比较：
     * ["{"key":"start_time","operator":"lt","value":{"type":"FIELD_TYPE","value":["end_time"]}}"]
     * 含义：start_time < end_time
     * 
     * 9. 复杂嵌套：
     * [
     *   "(",
     *   "{"key":"type","operator":"eq","value":{"type":"STRING","value":["A"]}}",
     *   "且",
     *   "(",
     *   "{"key":"score","operator":"gt","value":{"type":"NUMBER","value":["80"]}}",
     *   "或",
     *   "{"key":"level","operator":"in","value":{"type":"STRING","value":["high","premium"]}}",
     *   ")",
     *   ")",
     *   "或",
     *   "(",
     *   "{"key":"type","operator":"eq","value":{"type":"STRING","value":["B"]}}",
     *   "且",
     *   "{"key":"priority","operator":"eq","value":{"type":"STRING","value":["urgent"]}}",
     *   ")"
     * ]
     * 含义：(type = 'A' AND (score > 80 OR level IN ('high', 'premium'))) 
     *      OR (type = 'B' AND priority = 'urgent')
     * 
     * SparkFilterNode的纯Spark实现策略：
     *
     * 1. 完全Spark化：
     *    - 将所有过滤逻辑转换为Spark SQL表达式
     *    - 利用Spark的Catalyst优化器进行查询优化
     *    - 支持分布式并行处理
     *
     * 2. 逻辑转换：
     *    - tokens解析：使用栈结构处理逻辑操作符优先级
     *    - 条件构建：每个操作符转换为对应的Spark Column操作
     *    - 类型处理：自动进行数据类型转换和比较
     *
     * 3. 性能优化：
     *    - 谓词下推：过滤条件尽早应用，减少数据传输
     *    - 列式存储：利用Spark的列式处理优势
     *    - 向量化执行：批量处理提高CPU利用率
     *
     * 4. Spark SQL等价转换：
     *    - 复杂逻辑表达式 → Spark Column组合
     *    - 字段比较 → Column.equalTo/gt/lt等
     *    - 正则匹配 → Column.rlike
     *    - 空值检查 → Column.isNull/isNotNull
     * 
     * 使用示例代码：
     *
     * ```java
     * // 创建过滤属性
     * FilterNodeProperties properties = new FilterNodeProperties();
     * properties.setTokens(new String[]{
     *     "(",
     *     "{\"key\":\"age\",\"operator\":\"ge\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"18\"]}}",
     *     "且",
     *     "{\"key\":\"status\",\"operator\":\"eq\",\"value\":{\"type\":\"STRING\",\"value\":[\"active\"]}}",
     *     ")"
     * });
     *
     * // 创建Spark过滤节点
     * SparkFilterNode filterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
     *
     * // 执行过滤 - 纯Spark实现
     * NodeData result = filterNode.process(inputNodes, context);
     * ```
     *
     * Spark SQL等价查询：
     * ```sql
     * SELECT * FROM table
     * WHERE (age >= 18 AND status = 'active')
     * ```
     *
     * 性能优势：
     * - 分布式并行处理：多节点同时处理数据
     * - 内存计算：数据缓存在内存中，避免磁盘I/O
     * - 查询优化：Catalyst优化器自动优化查询计划
     * - 向量化执行：批量处理提高CPU效率
     * 
     * 注意事项：
     * 1. 确保Spark环境正确配置
     * 2. 复杂过滤条件可能需要更多内存
     * 3. 网络传输开销在小数据量时可能不划算
     * 4. 建议在生产环境中进行性能测试
     */
    
    /**
     * 常见过滤场景示例
     */
    public static class CommonFilterScenarios {
        
        /**
         * 场景1：用户筛选
         * 筛选年龄在18-65岁之间的活跃用户
         */
        public static String[] userFilter() {
            return new String[]{
                "(",
                "{\"key\":\"age\",\"operator\":\"ge\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"18\"]}}",
                "且",
                "{\"key\":\"age\",\"operator\":\"le\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"65\"]}}",
                ")",
                "且",
                "{\"key\":\"status\",\"operator\":\"eq\",\"value\":{\"type\":\"STRING\",\"value\":[\"active\"]}}"
            };
        }
        
        /**
         * 场景2：订单筛选
         * 筛选最近30天的大额订单
         */
        public static String[] orderFilter() {
            return new String[]{
                "{\"key\":\"amount\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"1000\"]}}",
                "且",
                "{\"key\":\"create_time\",\"operator\":\"ge\",\"value\":{\"type\":\"CONTROL_PARAM\",\"value\":[\"start_date\"]}}"
            };
        }
        
        /**
         * 场景3：产品筛选
         * 筛选特定类别的热销产品
         */
        public static String[] productFilter() {
            return new String[]{
                "{\"key\":\"category\",\"operator\":\"in\",\"value\":{\"type\":\"STRING\",\"value\":[\"electronics\",\"books\",\"clothing\"]}}",
                "且",
                "(",
                "{\"key\":\"sales_count\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"100\"]}}",
                "或",
                "{\"key\":\"rating\",\"operator\":\"ge\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"4.5\"]}}",
                ")"
            };
        }
    }
}
