package com.trs.police.service.node.spark.impl;

import com.trs.police.constant.StatisticType;
import com.trs.police.dto.node.properties.StatisticNodeProperties;
import com.trs.police.dto.node.properties.bean.GroupField;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;
import java.util.stream.Collectors;

import static com.trs.police.constant.StatisticType.*;

/**
 * Spark统计节点
 *
 * <AUTHOR>
 */
public class SparkStatisticNode extends SparkNode {

    public SparkStatisticNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        StatisticNodeProperties property = getPropertyAs(StatisticNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用统计操作
        Dataset<Row> statisticDataset = applySparkStatistic(dataset, property);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(statisticDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.STATISTIC;
    }

    /**
     * 应用Spark统计操作
     *
     * @param dataset 数据集
     * @param property 统计属性
     * @return 统计后的数据集
     */
    private Dataset<Row> applySparkStatistic(Dataset<Row> dataset, StatisticNodeProperties property) {
        // 获取分组字段
        List<String> groupFields = property.getGroupField().stream()
                .map(GroupField::getField)
                .collect(Collectors.toList());

        // 获取统计字段和类型
        String statisticField = property.getStatisticValue().getEnName();
        String outputField = property.getOutValue().getEnName();
        StatisticType statisticType = StatisticType.of(property.getStatisticType());

        Dataset<Row> result;

        if (groupFields.isEmpty()) {
            // 无分组的统计
            result = applyGlobalStatistic(dataset, statisticField, outputField, statisticType);
        } else {
            // 分组统计
            result = applyGroupedStatistic(dataset, groupFields, statisticField, outputField, statisticType);
        }

        return result;
    }

    /**
     * 应用全局统计（无分组）
     *
     * @param dataset 数据集
     * @param statisticField 统计字段
     * @param outputField 输出字段名
     * @param statisticType 统计类型
     * @return 统计结果
     */
    private Dataset<Row> applyGlobalStatistic(Dataset<Row> dataset, String statisticField, 
                                            String outputField, StatisticType statisticType) {
        switch (statisticType) {
            case COUNT:
                return dataset.agg(functions.count(statisticField).alias(outputField));
            case SUM:
                return dataset.agg(functions.sum(statisticField).alias(outputField));
            case AVG:
                return dataset.agg(functions.avg(statisticField).alias(outputField));
            case MAX:
                return dataset.agg(functions.max(statisticField).alias(outputField));
            case MIN:
                return dataset.agg(functions.min(statisticField).alias(outputField));
            default:
                throw new RuntimeException("不支持的统计类型: " + statisticType);
        }
    }

    /**
     * 应用分组统计
     *
     * @param dataset 数据集
     * @param groupFields 分组字段
     * @param statisticField 统计字段
     * @param outputField 输出字段名
     * @param statisticType 统计类型
     * @return 统计结果
     */
    private Dataset<Row> applyGroupedStatistic(Dataset<Row> dataset, List<String> groupFields, 
                                             String statisticField, String outputField, StatisticType statisticType) {
        // 构建分组列
        org.apache.spark.sql.Column[] groupColumns = groupFields.stream()
                .map(functions::col)
                .toArray(org.apache.spark.sql.Column[]::new);

        // 执行分组统计
        switch (statisticType) {
            case COUNT:
                return dataset.groupBy(groupColumns)
                        .agg(functions.count(statisticField).alias(outputField));
            case SUM:
                return dataset.groupBy(groupColumns)
                        .agg(functions.sum(statisticField).alias(outputField));
            case AVG:
                return dataset.groupBy(groupColumns)
                        .agg(functions.avg(statisticField).alias(outputField));
            case MAX:
                return dataset.groupBy(groupColumns)
                        .agg(functions.max(statisticField).alias(outputField));
            case MIN:
                return dataset.groupBy(groupColumns)
                        .agg(functions.min(statisticField).alias(outputField));
            default:
                throw new RuntimeException("不支持的统计类型: " + statisticType);
        }
    }
}
