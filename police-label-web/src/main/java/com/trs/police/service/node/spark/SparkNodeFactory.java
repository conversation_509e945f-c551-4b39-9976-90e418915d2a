package com.trs.police.service.node.spark;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.datatable.DataTableService;
import com.trs.police.service.fieldsService.FieldsService;
import com.trs.police.service.node.spark.impl.*;
import com.trs.police.service.shared.feature.FeatureFacade;
import com.trs.police.service.shared.label.LabelFacade;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * Spark节点工厂
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnProperty(name = "spark.enabled", havingValue = "true", matchIfMissing = false)
public class SparkNodeFactory {

    @Autowired
    private SparkSession sparkSession;

    @Autowired
    private DataTableService dataTableService;

    @Autowired
    private FieldsService fieldsService;

    @Autowired
    private FeatureFacade featureFacade;

    @Autowired
    private LabelFacade labelFacade;

    /**
     * 匹配Spark节点
     *
     * @param type 类型
     * @param nodeMeta nm
     * @param nodeProperties np
     * @return 节点
     */
    public Optional<SparkNode> nodeOf(Integer type, NodeMeta nodeMeta, String nodeProperties) {
        if (Objects.isNull(type)) {
            return Optional.empty();
        }
        if (NodeType.TABLE.equals(type)) {
            return Optional.of(new SparkTableNode(nodeMeta, nodeProperties, sparkSession, dataTableService, fieldsService));
        }
        if (NodeType.FILTER.equals(type)) {
            return Optional.of(new SparkFilterNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.ORDER.equals(type)) {
            return Optional.of(new SparkOrderNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.FEATURE_OUT.equals(type)) {
            return Optional.of(new SparkFeatureOutNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.FEATURE_IN.equals(type)) {
            return Optional.of(new SparkFeatureInputNode(nodeMeta, nodeProperties, sparkSession, featureFacade));
        }
        if (NodeType.STATISTIC.equals(type)) {
            return Optional.of(new SparkStatisticNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.DISTINCT.equals(type)) {
            return Optional.of(new SparkDistinctNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.TRANSFORM.equals(type)) {
            return Optional.of(new SparkConvertNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.LABEL_INPUT.equals(type)) {
            return Optional.of(new SparkLabelInputNode(nodeMeta, nodeProperties, sparkSession, labelFacade));
        }
        if (NodeType.LABEL_OUTPUT.equals(type)) {
            return Optional.of(new SparkLabelOutputNode(nodeMeta, nodeProperties, sparkSession));
        }
        if (NodeType.NEW_FIELD.equals(type)) {
            return Optional.of(new SparkNewFieldNode(nodeMeta, nodeProperties, sparkSession));
        }
        return Optional.empty();
    }
}
