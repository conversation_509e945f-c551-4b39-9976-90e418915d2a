package com.trs.police.service.node.spark;

import org.apache.spark.SparkConf;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * Spark配置类
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "spark.enabled", havingValue = "true", matchIfMissing = false)
public class SparkConfiguration {

    @Value("${spark.app.name:police-label-spark}")
    private String appName;

    @Value("${spark.master:local[*]}")
    private String master;

    @Value("${spark.executor.memory:2g}")
    private String executorMemory;

    @Value("${spark.driver.memory:1g}")
    private String driverMemory;

    @Value("${spark.executor.cores:2}")
    private String executorCores;

    @Value("${spark.sql.adaptive.enabled:true}")
    private String adaptiveQueryEnabled;

    @Value("${spark.sql.adaptive.coalescePartitions.enabled:true}")
    private String coalescePartitionsEnabled;

    private SparkSession sparkSession;

    /**
     * 创建SparkSession Bean
     *
     * @return SparkSession
     */
    @Bean
    public SparkSession sparkSession() {
        if (sparkSession == null) {
            SparkConf conf = new SparkConf()
                    .setAppName(appName)
                    .setMaster(master)
                    .set("spark.executor.memory", executorMemory)
                    .set("spark.driver.memory", driverMemory)
                    .set("spark.executor.cores", executorCores)
                    .set("spark.sql.adaptive.enabled", adaptiveQueryEnabled)
                    .set("spark.sql.adaptive.coalescePartitions.enabled", coalescePartitionsEnabled)
                    .set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
                    .set("spark.sql.execution.arrow.pyspark.enabled", "true");

            sparkSession = SparkSession.builder()
                    .config(conf)
                    .getOrCreate();
        }
        return sparkSession;
    }

    /**
     * 应用关闭时清理Spark资源
     */
    @PreDestroy
    public void cleanup() {
        if (sparkSession != null) {
            sparkSession.stop();
        }
    }
}
