# Spark节点实现说明

## 概述

本模块为`NodeService.previewNode`方法中的每个节点类型提供了基于Apache Spark的实现，以提高大数据处理的性能和可扩展性。

## 架构设计

### 核心组件

1. **SparkNode** - Spark节点基类，类似于原始的Node类
2. **SparkNodeFactory** - Spark节点工厂，负责创建各种类型的Spark节点
3. **SparkNodeService** - Spark节点服务，提供与原始NodeService相同的接口
4. **SparkDataConverter** - 数据转换工具，负责NodeData与Spark Dataset之间的转换
5. **SparkConfiguration** - Spark配置类，管理Spark会话和相关配置

### 节点实现

每个原始节点类型都有对应的Spark实现：

- `SparkTableNode` - 表格输入节点（Spark实现）
- `SparkFilterNode` - 过滤节点（Spark实现，完全兼容原始逻辑）
- `SparkOrderNode` - 排序节点（Spark实现）
- `SparkStatisticNode` - 统计节点（Spark实现，支持多分组字段）
- `SparkDistinctNode` - 去重节点（Spark实现）
- `SparkConvertNode` - 转换节点（Spark实现）
- `SparkFeatureOutNode` - 特征输出节点（Spark实现）
- `SparkFeatureInputNode` - 特征输入节点（Spark实现）
- `SparkLabelInputNode` - 标签输入节点（Spark实现）
- `SparkLabelOutputNode` - 标签输出节点（Spark实现）
- `SparkNewFieldNode` - 新字段节点（Spark实现）

#### 特别优化的节点

**SparkFilterNode** - 智能过滤策略：
- 小数据量（<10k行）：使用原始逻辑确保100%兼容性
- 大数据量（>=10k行）：使用Spark分布式处理提升性能
- 支持所有原始过滤操作：eq, ne, gt, lt, ge, le, in, notIn, like, notLike, isNull, notNull, empty, notEmpty, regularMatch, regularNotMatch
- 支持复杂逻辑组合：且(AND), 或(OR), 非(NOT), 括号分组
- 支持控制参数和字段间比较

**SparkStatisticNode** - 多维度统计：
- 支持任意数量的分组字段组合
- 支持时间字段的特殊分组（按秒/分/时/天/周/月/年）
- 利用Spark的分布式聚合能力处理大数据量统计

## 配置说明

### 启用Spark

在`application.yml`或`application-spark.yml`中配置：

```yaml
spark:
  enabled: true
  app:
    name: police-label-spark
  master: local[*]  # 本地模式，生产环境可配置为集群地址
  executor:
    memory: 2g
    cores: 2
  driver:
    memory: 1g
```

### 配置参数说明

- `spark.enabled`: 是否启用Spark处理（默认false）
- `spark.app.name`: Spark应用名称
- `spark.master`: Spark集群地址
- `spark.executor.memory`: 执行器内存
- `spark.executor.cores`: 执行器CPU核数
- `spark.driver.memory`: 驱动程序内存

## 使用方式

### 自动切换

当`spark.enabled=true`时，`NodeService.previewNode`方法会自动使用Spark实现。如果Spark处理失败，会自动回退到原始实现。

### 手动使用

也可以直接注入`SparkNodeService`来使用Spark实现：

```java
@Autowired
private SparkNodeService sparkNodeService;

public NodeData processWithSpark(ProcessDTO dto) {
    return sparkNodeService.previewNode(dto);
}
```

## 性能优化

### 智能处理策略

**SparkFilterNode**采用智能切换策略：
```java
// 根据数据量自动选择处理方式
boolean useSparkOptimization = dataSize > 10000;
if (useSparkOptimization && canUseSparkOptimization(property)) {
    return processWithSparkDataset(input, property, context);
} else {
    return processWithOriginalLogic(input, property, context);
}
```

### 数据缓存

Spark实现中使用了数据缓存来提高性能：

```java
dataset = dataset.cache(); // 缓存数据集
```

### 自适应查询执行

启用了Spark SQL的自适应查询执行功能：

```yaml
spark:
  sql:
    adaptive:
      enabled: true
      coalescePartitions:
        enabled: true
```

### 性能基准测试

提供了完整的性能基准测试工具：
```java
// 运行性能对比测试
FilterNodeBenchmark benchmark = new FilterNodeBenchmark();
benchmark.runBenchmark();
```

测试结果显示：
- 小数据量（<10k）：原始逻辑性能更优
- 大数据量（>=10k）：Spark处理性能显著提升
- 复杂过滤条件：Spark并行处理优势明显

## 扩展说明

### 添加新的转换逻辑

在各个Spark节点实现中，可以添加更多的Spark特有优化：

1. **分区优化** - 根据数据特征调整分区策略
2. **广播变量** - 对小表使用广播join
3. **列式存储** - 使用Parquet等列式格式
4. **向量化执行** - 启用向量化执行引擎

### 自定义数据源

可以扩展`SparkTableNode`来支持更多数据源：

- HDFS
- S3
- Kafka
- HBase
- Cassandra

## 注意事项

1. **内存管理** - 确保为Spark分配足够的内存
2. **资源清理** - Spark会话会在应用关闭时自动清理
3. **错误处理** - 实现了自动回退机制，确保系统稳定性
4. **兼容性** - 保持与原始NodeService完全兼容的接口

## 监控和调试

### Spark UI

当Spark应用运行时，可以通过Spark UI监控任务执行情况：
- 默认地址：http://localhost:4040

### 日志配置

可以调整Spark相关组件的日志级别：

```yaml
logging:
  level:
    org.apache.spark: WARN
    org.spark_project: WARN
    org.apache.hadoop: WARN
```

## 未来改进

1. **动态资源分配** - 根据数据量动态调整Spark资源
2. **SQL优化** - 使用Spark SQL的成本优化器
3. **流处理支持** - 集成Spark Streaming处理实时数据
4. **机器学习** - 集成Spark MLlib进行数据挖掘
