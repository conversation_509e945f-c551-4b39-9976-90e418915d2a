package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.FeatureOutPutProperties;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * Spark特征输出节点
 *
 * <AUTHOR>
 */
public class SparkFeatureOutNode extends SparkNode {

    public SparkFeatureOutNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        FeatureOutPutProperties property = getPropertyAs(FeatureOutPutProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 特征输出节点通常只是传递数据，可以在这里添加一些优化
        dataset = dataset.cache(); // 缓存数据以提高后续访问性能
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(dataset, input);
        result.setNodeMeta(nodeMeta);
        result.setTotalCount(input.getTotalCount());
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.FEATURE_OUT;
    }
}
