package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.LabelInputProperties;
import com.trs.police.dto.node.ProcessDTO;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.shared.label.LabelFacade;
import com.trs.police.service.node.spark.SparkNodeService;
import com.trs.web.builder.util.BeanFactoryHolder;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * Spark标签输入节点
 *
 * <AUTHOR>
 */
public class SparkLabelInputNode extends SparkNode {

    private LabelFacade labelFacade;

    public SparkLabelInputNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark, LabelFacade labelFacade) {
        super(nodeMeta, nodeProperties, spark);
        this.labelFacade = labelFacade;
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        LabelInputProperties property = getPropertyAs(LabelInputProperties.class);
        ProcessDTO process = labelFacade.getProcess(property.getLabelId());
        
        // 使用Spark节点服务处理标签流程
        SparkNodeService sparkNodeService = BeanFactoryHolder.getBean(SparkNodeService.class).get();
        NodeData nodeData = sparkNodeService.previewNode(process);
        
        return nodeData;
    }

    @Override
    public Integer nodeType() {
        return NodeType.LABEL_INPUT;
    }
}
