package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.NewFieldProperties;
import com.trs.police.dto.node.properties.bean.NewField;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;

/**
 * Spark新字段节点
 *
 * <AUTHOR>
 */
public class SparkNewFieldNode extends SparkNode {

    public SparkNewFieldNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NewFieldProperties property = getPropertyAs(NewFieldProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用新字段操作
        Dataset<Row> newFieldDataset = applySparkNewFields(dataset, property, context);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(newFieldDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.NEW_FIELD;
    }

    /**
     * 应用Spark新字段操作
     *
     * @param dataset 数据集
     * @param property 新字段属性
     * @param context 上下文
     * @return 添加新字段后的数据集
     */
    private Dataset<Row> applySparkNewFields(Dataset<Row> dataset, NewFieldProperties property, NodeContext context) {
        if (property.getOutValue() == null || property.getOutValue().isEmpty()) {
            return dataset;
        }

        Dataset<Row> result = dataset;

        // 为每个新字段添加列
        for (NewField newField : property.getOutValue()) {
            result = addNewField(result, newField, context);
        }

        return result;
    }

    /**
     * 添加新字段
     *
     * @param dataset 数据集
     * @param newField 新字段定义
     * @param context 上下文
     * @return 添加字段后的数据集
     */
    private Dataset<Row> addNewField(Dataset<Row> dataset, NewField newField, NodeContext context) {
        String fieldName = newField.getEnName();
        String fieldExpression = newField.getExpression();
        String fieldType = newField.getFieldType();

        // 根据字段表达式类型处理
        if (fieldExpression == null || fieldExpression.isEmpty()) {
            // 如果没有表达式，添加空值字段
            return dataset.withColumn(fieldName, functions.lit(null));
        }

        // 解析字段表达式
        org.apache.spark.sql.Column column = parseFieldExpression(fieldExpression, context);
        
        // 根据字段类型进行类型转换
        column = castToFieldType(column, fieldType);

        return dataset.withColumn(fieldName, column);
    }

    /**
     * 解析字段表达式
     *
     * @param expression 表达式
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseFieldExpression(String expression, NodeContext context) {
        // 这里需要根据具体的表达式语法进行解析
        // 简化实现，支持基本的表达式类型

        if (expression.startsWith("field:")) {
            // 字段引用
            String fieldName = expression.substring(6);
            return functions.col(fieldName);
        } else if (expression.startsWith("const:")) {
            // 常量值
            String value = expression.substring(6);
            return functions.lit(value);
        } else if (expression.startsWith("control:")) {
            // 控制参数
            String controlName = expression.substring(8);
            Object controlValue = context.getControlValueMap().get(controlName);
            return functions.lit(controlValue);
        } else if (expression.contains("+")) {
            // 加法运算
            return parseArithmeticExpression(expression, "+", context);
        } else if (expression.contains("-")) {
            // 减法运算
            return parseArithmeticExpression(expression, "-", context);
        } else if (expression.contains("*")) {
            // 乘法运算
            return parseArithmeticExpression(expression, "*", context);
        } else if (expression.contains("/")) {
            // 除法运算
            return parseArithmeticExpression(expression, "/", context);
        } else if (expression.startsWith("concat(")) {
            // 字符串连接
            return parseConcatExpression(expression, context);
        } else if (expression.startsWith("substring(")) {
            // 字符串截取
            return parseSubstringExpression(expression, context);
        } else if (expression.startsWith("upper(")) {
            // 大写转换
            return parseUpperExpression(expression, context);
        } else if (expression.startsWith("lower(")) {
            // 小写转换
            return parseLowerExpression(expression, context);
        } else {
            // 默认作为常量处理
            return functions.lit(expression);
        }
    }

    /**
     * 解析算术表达式
     *
     * @param expression 表达式
     * @param operator 操作符
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseArithmeticExpression(String expression, String operator, NodeContext context) {
        String[] parts = expression.split("\\" + operator, 2);
        if (parts.length == 2) {
            org.apache.spark.sql.Column left = parseFieldExpression(parts[0].trim(), context);
            org.apache.spark.sql.Column right = parseFieldExpression(parts[1].trim(), context);
            
            switch (operator) {
                case "+":
                    return left.plus(right);
                case "-":
                    return left.minus(right);
                case "*":
                    return left.multiply(right);
                case "/":
                    return left.divide(right);
                default:
                    return left;
            }
        }
        return functions.lit(expression);
    }

    /**
     * 解析字符串连接表达式
     *
     * @param expression 表达式
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseConcatExpression(String expression, NodeContext context) {
        // 简化实现：concat(field1, field2, ...)
        String params = expression.substring(7, expression.length() - 1); // 去掉 "concat(" 和 ")"
        String[] fields = params.split(",");
        
        org.apache.spark.sql.Column[] columns = new org.apache.spark.sql.Column[fields.length];
        for (int i = 0; i < fields.length; i++) {
            columns[i] = parseFieldExpression(fields[i].trim(), context);
        }
        
        return functions.concat(columns);
    }

    /**
     * 解析字符串截取表达式
     *
     * @param expression 表达式
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseSubstringExpression(String expression, NodeContext context) {
        // 简化实现：substring(field, start, length)
        String params = expression.substring(10, expression.length() - 1); // 去掉 "substring(" 和 ")"
        String[] parts = params.split(",");
        
        if (parts.length >= 2) {
            org.apache.spark.sql.Column field = parseFieldExpression(parts[0].trim(), context);
            int start = Integer.parseInt(parts[1].trim());
            
            if (parts.length >= 3) {
                int length = Integer.parseInt(parts[2].trim());
                return functions.substring(field, start, length);
            } else {
                return functions.substring(field, start, Integer.MAX_VALUE);
            }
        }
        
        return functions.lit(expression);
    }

    /**
     * 解析大写转换表达式
     *
     * @param expression 表达式
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseUpperExpression(String expression, NodeContext context) {
        String field = expression.substring(6, expression.length() - 1); // 去掉 "upper(" 和 ")"
        return functions.upper(parseFieldExpression(field, context));
    }

    /**
     * 解析小写转换表达式
     *
     * @param expression 表达式
     * @param context 上下文
     * @return Spark列表达式
     */
    private org.apache.spark.sql.Column parseLowerExpression(String expression, NodeContext context) {
        String field = expression.substring(6, expression.length() - 1); // 去掉 "lower(" 和 ")"
        return functions.lower(parseFieldExpression(field, context));
    }

    /**
     * 根据字段类型进行类型转换
     *
     * @param column 列
     * @param fieldType 字段类型
     * @return 转换后的列
     */
    private org.apache.spark.sql.Column castToFieldType(org.apache.spark.sql.Column column, String fieldType) {
        if (fieldType == null) {
            return column;
        }
        
        switch (fieldType) {
            case "1": // 字符串
                return column.cast("string");
            case "2": // 整数
                return column.cast("long");
            case "3": // 小数
                return column.cast("double");
            case "4": // 日期
                return column.cast("date");
            case "5": // 时间戳
                return column.cast("timestamp");
            default:
                return column;
        }
    }
}
