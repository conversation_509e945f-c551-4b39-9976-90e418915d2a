package com.trs.police.service.node.spark.impl;

import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;

import java.util.List;

/**
 * Spark标签输出节点
 *
 * <AUTHOR>
 */
public class SparkLabelOutputNode extends SparkNode {

    public SparkLabelOutputNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 标签输出节点通常只是传递数据，可以在这里添加一些优化
        dataset = dataset.cache(); // 缓存数据以提高后续访问性能
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(dataset, input);
        result.setNodeMeta(nodeMeta);
        result.setTotalCount(input.getTotalCount());
        
        // 标签输出节点的输出字段取决于输入字段
        nodeMeta.setOutputRowMeta(input.getNodeMeta().getOutputRowMeta());
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.LABEL_OUTPUT;
    }
}
