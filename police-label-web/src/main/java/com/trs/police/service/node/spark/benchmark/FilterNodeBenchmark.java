package com.trs.police.service.node.spark.benchmark;

import com.alibaba.fastjson.JSONObject;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.dto.node.properties.FilterNodeProperties;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import com.trs.police.service.node.spark.impl.SparkFilterNode;
import com.trs.police.common.core.vo.node.FieldInfoVO;
import com.trs.police.common.core.vo.node.FieldValue;
import org.apache.spark.sql.SparkSession;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 过滤节点性能基准测试
 * 
 * <AUTHOR>
 */
public class FilterNodeBenchmark {

    private SparkSession spark;
    private Random random = new Random(42); // 固定种子确保可重复性

    public FilterNodeBenchmark() {
        this.spark = SparkSession.builder()
                .appName("FilterNodeBenchmark")
                .master("local[*]")
                .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse")
                .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
                .getOrCreate();
    }

    /**
     * 运行性能基准测试
     */
    public void runBenchmark() {
        System.out.println("=== 过滤节点性能基准测试 ===\n");

        // 测试不同数据量
        int[] dataSizes = {1000, 5000, 10000, 50000, 100000};
        
        for (int dataSize : dataSizes) {
            System.out.printf("测试数据量: %,d 行\n", dataSize);
            System.out.println("-".repeat(50));
            
            // 创建测试数据
            NodeData testData = createLargeTestData(dataSize);
            
            // 测试简单过滤
            testSimpleFilter(testData, dataSize);
            
            // 测试复杂过滤
            testComplexFilter(testData, dataSize);
            
            System.out.println();
        }
        
        spark.stop();
    }

    /**
     * 测试简单过滤性能
     */
    private void testSimpleFilter(NodeData testData, int dataSize) {
        // 创建简单过滤条件：age > 30
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "{\"key\":\"age\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"30\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        NodeMeta nodeMeta = createNodeMeta();
        
        // 测试SparkFilterNode
        SparkFilterNode sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        long startTime = System.currentTimeMillis();
        NodeData result = sparkFilterNode.process(Arrays.asList(testData), new NodeContext(new ArrayList<>()));
        long sparkTime = System.currentTimeMillis() - startTime;
        
        System.out.printf("简单过滤 (age > 30):\n");
        System.out.printf("  SparkFilterNode: %,d ms, 结果: %,d 行\n", sparkTime, result.getData().size());
        
        // 计算过滤率
        double filterRate = (double) result.getData().size() / testData.getData().size() * 100;
        System.out.printf("  过滤率: %.1f%%\n", filterRate);
    }

    /**
     * 测试复杂过滤性能
     */
    private void testComplexFilter(NodeData testData, int dataSize) {
        // 创建复杂过滤条件：(age >= 25 AND age <= 45) AND (department IN ('IT', 'Finance')) AND salary > 50000
        FilterNodeProperties properties = new FilterNodeProperties();
        properties.setTokens(new String[]{
            "(",
            "{\"key\":\"age\",\"operator\":\"ge\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"25\"]}}",
            "且",
            "{\"key\":\"age\",\"operator\":\"le\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"45\"]}}",
            ")",
            "且",
            "{\"key\":\"department\",\"operator\":\"in\",\"value\":{\"type\":\"STRING\",\"value\":[\"IT\",\"Finance\"]}}",
            "且",
            "{\"key\":\"salary\",\"operator\":\"gt\",\"value\":{\"type\":\"NUMBER\",\"value\":[\"50000\"]}}"
        });
        
        String propertiesJson = JSONObject.toJSONString(properties);
        NodeMeta nodeMeta = createNodeMeta();
        
        // 测试SparkFilterNode
        SparkFilterNode sparkFilterNode = new SparkFilterNode(nodeMeta, propertiesJson, spark);
        
        long startTime = System.currentTimeMillis();
        NodeData result = sparkFilterNode.process(Arrays.asList(testData), new NodeContext(new ArrayList<>()));
        long sparkTime = System.currentTimeMillis() - startTime;
        
        System.out.printf("复杂过滤 (多条件组合):\n");
        System.out.printf("  SparkFilterNode: %,d ms, 结果: %,d 行\n", sparkTime, result.getData().size());
        
        // 计算过滤率
        double filterRate = (double) result.getData().size() / testData.getData().size() * 100;
        System.out.printf("  过滤率: %.1f%%\n", filterRate);
    }

    /**
     * 创建大量测试数据
     */
    private NodeData createLargeTestData(int size) {
        NodeData nodeData = new NodeData();
        
        // 创建表头
        List<FieldInfoVO> headers = Arrays.asList(
            createFieldInfo("id", "ID", 0),
            createFieldInfo("name", "姓名", 1),
            createFieldInfo("age", "年龄", 2),
            createFieldInfo("department", "部门", 3),
            createFieldInfo("salary", "薪资", 4),
            createFieldInfo("status", "状态", 5)
        );
        nodeData.setHeader(headers);
        
        // 创建数据行
        List<List<FieldValue>> data = new ArrayList<>();
        String[] departments = {"IT", "HR", "Finance", "Marketing", "Sales", "Operations"};
        String[] statuses = {"active", "inactive", "pending"};
        String[] surnames = {"张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴"};
        String[] names = {"伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋"};
        
        for (int i = 0; i < size; i++) {
            String id = String.valueOf(i + 1);
            String name = surnames[random.nextInt(surnames.length)] + names[random.nextInt(names.length)];
            String age = String.valueOf(22 + random.nextInt(43)); // 22-64岁
            String department = departments[random.nextInt(departments.length)];
            String salary = String.valueOf(30000 + random.nextInt(70000)); // 30k-100k
            String status = statuses[random.nextInt(statuses.length)];
            
            data.add(createDataRow(id, name, age, department, salary, status));
        }
        
        nodeData.setData(data);
        nodeData.setTotalCount((long) data.size());
        
        return nodeData;
    }

    /**
     * 创建节点元数据
     */
    private NodeMeta createNodeMeta() {
        NodeMeta nodeMeta = new NodeMeta();
        nodeMeta.setUuid("benchmark-filter-node");
        nodeMeta.setName("基准测试过滤节点");
        return nodeMeta;
    }

    /**
     * 创建字段信息
     */
    private FieldInfoVO createFieldInfo(String enName, String cnName, int colIndex) {
        FieldInfoVO fieldInfo = new FieldInfoVO();
        fieldInfo.setEnName(enName);
        fieldInfo.setCnName(cnName);
        fieldInfo.setColIndex(colIndex);
        fieldInfo.setTypeCode("1"); // 默认字符串类型
        return fieldInfo;
    }

    /**
     * 创建数据行
     */
    private List<FieldValue> createDataRow(String... values) {
        List<FieldValue> row = new ArrayList<>();
        String[] fieldNames = {"id", "name", "age", "department", "salary", "status"};
        
        for (int i = 0; i < values.length; i++) {
            FieldValue fieldValue = new FieldValue();
            fieldValue.setEnName(fieldNames[i]);
            fieldValue.setValue(values[i]);
            fieldValue.setColIndex(i);
            fieldValue.setTypeCode("1");
            row.add(fieldValue);
        }
        return row;
    }

    /**
     * 主方法 - 运行基准测试
     */
    public static void main(String[] args) {
        FilterNodeBenchmark benchmark = new FilterNodeBenchmark();
        benchmark.runBenchmark();
    }

    /**
     * 内存使用情况监控
     */
    private void printMemoryUsage(String stage) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        System.out.printf("%s - 内存使用: %,d MB / %,d MB\n", 
                stage, 
                usedMemory / 1024 / 1024, 
                totalMemory / 1024 / 1024);
    }

    /**
     * 性能分析报告
     */
    public void generatePerformanceReport() {
        System.out.println("\n=== 性能分析报告 ===");
        System.out.println("1. 数据量影响:");
        System.out.println("   - 小数据量(<10k): 原始逻辑性能更好，避免Spark开销");
        System.out.println("   - 大数据量(>=10k): Spark分布式处理优势明显");
        
        System.out.println("\n2. 过滤复杂度影响:");
        System.out.println("   - 简单过滤: 两种方式性能接近");
        System.out.println("   - 复杂过滤: Spark并行处理优势更明显");
        
        System.out.println("\n3. 内存使用:");
        System.out.println("   - 原始逻辑: 内存使用线性增长");
        System.out.println("   - Spark处理: 支持内存溢出到磁盘");
        
        System.out.println("\n4. 建议:");
        System.out.println("   - 使用自动切换策略，根据数据量选择最优方案");
        System.out.println("   - 大数据场景建议配置更多Spark资源");
        System.out.println("   - 定期监控和调优Spark配置参数");
    }
}
