package com.trs.police.service.node.spark.impl;

import com.trs.police.dto.node.properties.DistinctNodeProperties;
import com.trs.police.dto.node.properties.bean.GroupField;
import com.trs.police.constant.NodeType;
import com.trs.police.dto.node.NodeContext;
import com.trs.police.service.node.spark.SparkNode;
import com.trs.police.dto.node.NodeMeta;
import com.trs.police.service.feature.application.DTO.vo.NodeData;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.apache.spark.sql.functions;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Spark去重节点
 *
 * <AUTHOR>
 */
public class SparkDistinctNode extends SparkNode {

    public SparkDistinctNode(NodeMeta nodeMeta, String nodeProperties, SparkSession spark) {
        super(nodeMeta, nodeProperties, spark);
    }

    @Override
    protected NodeData process(List<NodeData> inputNode, NodeContext context) {
        DistinctNodeProperties property = getPropertyAs(DistinctNodeProperties.class);
        NodeData input = inputNode.get(0);
        
        // 转换为Spark Dataset
        Dataset<Row> dataset = nodeDataToDataset(input);
        
        // 应用去重操作
        Dataset<Row> distinctDataset = applySparkDistinct(dataset, property);
        
        // 转换回NodeData
        NodeData result = datasetToNodeData(distinctDataset, input);
        result.setNodeMeta(nodeMeta);
        
        return result;
    }

    @Override
    public Integer nodeType() {
        return NodeType.DISTINCT;
    }

    /**
     * 应用Spark去重操作
     *
     * @param dataset 数据集
     * @param property 去重属性
     * @return 去重后的数据集
     */
    private Dataset<Row> applySparkDistinct(Dataset<Row> dataset, DistinctNodeProperties property) {
        if (property.getGroupField() == null || property.getGroupField().isEmpty()) {
            // 全字段去重
            return dataset.distinct();
        }

        // 按指定字段去重
        List<String> distinctFields = property.getGroupField().stream()
                .map(GroupField::getField)
                .collect(Collectors.toList());

        // 使用窗口函数进行去重，保留每组的第一条记录
        return applyFieldBasedDistinct(dataset, distinctFields, property);
    }

    /**
     * 基于字段的去重操作
     *
     * @param dataset 数据集
     * @param distinctFields 去重字段
     * @param property 去重属性
     * @return 去重后的数据集
     */
    private Dataset<Row> applyFieldBasedDistinct(Dataset<Row> dataset, List<String> distinctFields, 
                                               DistinctNodeProperties property) {
        // 构建分组列
        org.apache.spark.sql.Column[] groupColumns = distinctFields.stream()
                .map(functions::col)
                .toArray(org.apache.spark.sql.Column[]::new);

        // 根据去重类型选择不同的策略
        if (property.getGroupField().size() == 1) {
            GroupField groupField = property.getGroupField().get(0);
            String distinctType = groupField.getDistinctType();
            
            if ("FIRST".equals(distinctType) || "1".equals(distinctType)) {
                // 保留第一条记录
                return dataset.dropDuplicates(distinctFields.toArray(new String[0]));
            } else if ("LAST".equals(distinctType) || "2".equals(distinctType)) {
                // 保留最后一条记录 - 需要先排序再去重
                // 这里假设有一个隐式的行号或时间戳字段用于排序
                return dataset.orderBy(functions.monotonically_increasing_id().desc())
                        .dropDuplicates(distinctFields.toArray(new String[0]))
                        .orderBy(functions.monotonically_increasing_id());
            } else if ("MAX".equals(distinctType) || "3".equals(distinctType)) {
                // 保留最大值记录 - 使用窗口函数
                return applyWindowBasedDistinct(dataset, distinctFields, "MAX");
            } else if ("MIN".equals(distinctType) || "4".equals(distinctType)) {
                // 保留最小值记录 - 使用窗口函数
                return applyWindowBasedDistinct(dataset, distinctFields, "MIN");
            }
        }

        // 默认去重策略
        return dataset.dropDuplicates(distinctFields.toArray(new String[0]));
    }

    /**
     * 基于窗口函数的去重操作
     *
     * @param dataset 数据集
     * @param distinctFields 去重字段
     * @param aggregationType 聚合类型
     * @return 去重后的数据集
     */
    private Dataset<Row> applyWindowBasedDistinct(Dataset<Row> dataset, List<String> distinctFields, 
                                                String aggregationType) {
        // 这里需要根据具体的业务逻辑实现窗口函数去重
        // 由于需要知道具体的值字段，这里提供一个简化的实现
        
        // 使用row_number()窗口函数
        org.apache.spark.sql.expressions.WindowSpec windowSpec = 
                org.apache.spark.sql.expressions.Window.partitionBy(
                        distinctFields.stream()
                                .map(functions::col)
                                .toArray(org.apache.spark.sql.Column[]::new)
                );

        if ("MAX".equals(aggregationType)) {
            // 按某个字段降序排列，取第一条
            windowSpec = windowSpec.orderBy(functions.monotonically_increasing_id().desc());
        } else {
            // 按某个字段升序排列，取第一条
            windowSpec = windowSpec.orderBy(functions.monotonically_increasing_id().asc());
        }

        return dataset.withColumn("row_number", functions.row_number().over(windowSpec))
                .filter(functions.col("row_number").equalTo(1))
                .drop("row_number");
    }
}
