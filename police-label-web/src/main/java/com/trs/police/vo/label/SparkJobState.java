package com.trs.police.vo.label;

import com.trs.police.enums.LabelCalculationStatus;
import lombok.Data;

import java.util.List;

@Data
public class SparkJobState {

    private LabelCalculationStatus jobState;

    private String errorMessage;

    public void setJobState(String jobState) {
        if("FAILED".equals(jobState)){
            this.jobState = LabelCalculationStatus.FAILED;
        }else if ("FINISHED".equals(jobState)){
            this.jobState = LabelCalculationStatus.SUCCESS;
        }else {
            this.jobState = LabelCalculationStatus.RUNNING;
        }
    }
}
